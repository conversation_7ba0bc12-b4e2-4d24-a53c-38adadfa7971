package com.trs.moye.storage.engine.handler;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.storage.engine.pojo.request.search.Condition;
import com.trs.moye.storage.engine.pojo.request.search.ValueObject;
import com.trs.moye.storage.engine.utils.SqlUtils;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * contain条件处理 包含（模糊）
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
@Slf4j
public class ContainConditionHandler implements SqlConditionHandler {

    @Override
    public void handleSql(StringBuilder whereClause, ConnectionType connectionType,
            Condition condition) {
        String key = condition.getKey().getEnName(); // 获取字段名
        List<ValueObject> values = condition.getValues(); // 获取值

        if (connectionType == ConnectionType.ELASTIC_SEARCH
                && condition.getKey().getType() == FieldType.TEXT) {
            String conditions = values.stream()
                    .map(value -> "MATCH(" + key + ", '" + value.getValue() + "')")
                    .collect(Collectors.joining(" OR "));

            if (!conditions.isEmpty()) {
                conditions = "(" + conditions + ")";
            }
            whereClause.append(conditions);
        } else {
            whereClause.append(" ( ");
            for (int j = 0; j < values.size(); j++) {
                if (j > 0) {
                    whereClause.append(" OR "); // 使用 OR 连接多个条件
                }
                if (connectionType.notNeedDoubleQuotes()) {
                    whereClause.append(key).append(" LIKE '%").append(values.get(j).getValue())
                            .append("%'");
                } else {
                    whereClause.append("\"").append(key).append("\" LIKE '%")
                            .append(values.get(j).getValue()).append("%'");
                }
            }
            whereClause.append(" ) ");

            // notNullCondition(values, connectionType, whereClause, condition);
        }
    }

    /**
     * @param clause SQL片段
     * @param key 字段名
     * @param values 值列表
     */
    @Override
    public void handleHyBase(StringBuilder clause, String key, List<ValueObject> values) {
        clause.append("(");
        for (int j = 0; j < values.size(); j++) {
            if (j > 0) {
                clause.append(" OR ");
            }
            String value = values.get(j).getValue();
            // 如果没有通配符，自动包装为%value%
            if (value != null && !value.contains("%") && !value.contains("_") && !value.contains("*")) {
                value = "%" + value + "%";
            }
            // 判断是否为like模式
            if (value != null && (value.contains("%") || value.contains("_") || value.contains("*"))) {
                // 转换MySQL like模式和*为正则
                StringBuilder regex = new StringBuilder();
                regex.append("/");
                for (int i = 0; i < value.length(); i++) {
                    char c = value.charAt(i);
                    if (c == '%' || c == '*') {
                        regex.append(".*");
                    } else if (c == '_') {
                        regex.append(".");
                    } else {
                        if (".[]{}()+-?^$|\\".indexOf(c) >= 0) {
                            regex.append("\\");
                        }
                        regex.append(c);
                    }
                }
                regex.append("/");
                clause.append(key).append(":").append(regex);
            } else {
                // 理论上不会走到这里
                clause.append(key).append(":(").append(value).append("*)");
            }
        }
        clause.append(")");
    }
}
