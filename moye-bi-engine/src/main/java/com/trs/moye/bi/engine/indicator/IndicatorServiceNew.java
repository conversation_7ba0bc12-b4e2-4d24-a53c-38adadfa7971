package com.trs.moye.bi.engine.indicator;

import static com.trs.moye.bi.engine.indicator.constant.BiIndicatorConstants.GROUP_FIELD;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.base.data.indicator.enums.IndicatorFieldType;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.bi.engine.indicator.calculation.CalculationUtils;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.processor.DataQueryProcessor;
import com.trs.moye.bi.engine.indicator.util.DataConnectionValidator;
import com.trs.moye.bi.engine.indicator.util.DataGrouping;
import com.trs.moye.bi.engine.indicator.util.IndicatorDataSorter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 指标服务实现类
 * <p>
 * 负责处理指标数据的查询和计算请求
 *
 * <AUTHOR>
 * @since 2025/06/26 10:24:34
 */
@Slf4j
@Service
public class IndicatorServiceNew {

    @Resource
    private IndicatorFieldMapper indicatorFieldMapper;
    @Resource
    private DataModelFieldMapper dataModelFieldMapper;
    @Resource
    private DataGrouping dataGrouping;
    @Resource
    private DataConnectionValidator dataConnectionValidator;
    @Resource
    private DataQueryProcessor dataQueryProcessor;

    /**
     * 清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("指标服务开始清理资源");
        // 清理可能残留的上下文
        IndicatorDataContext.clear();
    }

    /**
     * 指标查询的公共入口点
     *
     * @param connectionId 数据连接ID
     * @param request      指标数据查询参数
     * @return 查询和计算后的分页结果
     */
    public PageResponse<Map<String, Object>> indicatorQuery(Integer connectionId, IndicatorDataSearchParams request) {
        try {
            log.info("开始指标查询, connectionId: {}, dataModelId: {}", connectionId, request.getDataModelId());

            // 验证参数和连接
            dataConnectionValidator.validateRequest(request);
            DataConnection dataConnection = dataConnectionValidator.validateAndGetConnection(connectionId);

            return handleDataQuery(dataConnection, request);

        } catch (IndicatorQueryException e) {
            log.error("指标查询失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("指标查询发生未知错误, connectionId: {}, dataModelId: {}",
                connectionId, request.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("指标查询", e);
        }
    }

    /**
     * 指标查询（带分组）的公共入口点
     *
     * @param connectionId 数据连接ID
     * @param request      指标数据查询参数
     * @return 查询、计算和分组后的结果
     */
    public PageResponse<Map<String, Object>> indicatorQueryWithGrouping(Integer connectionId,
        IndicatorDataSearchParams request) {
        try {
            log.info("开始带分组的指标查询, connectionId: {}, dataModelId: {}",
                connectionId, request.getDataModelId());

            // 验证参数和连接
            dataConnectionValidator.validateRequest(request);
            DataConnection dataConnection = dataConnectionValidator.validateAndGetConnection(connectionId);

            // 构建查询上下文
            FieldGroups fieldGroups = getFieldGroups(request.getDataModelId());
            QueryContext queryContext = dataConnectionValidator.buildQueryContext(
                dataConnection, request, fieldGroups);

            // 获取基础数据
            PageResponse<Map<String, Object>> response = handleDataQueryWithContext(queryContext);
            List<Map<String, Object>> items = response.getItems();

            if (items.isEmpty()) {
                log.info("基础数据为空，返回空结果");
                return new PageResponse<>();
            }

            // 执行数据分组
            List<Map<String, Object>> result = performDataGrouping(items, queryContext);

            // 应用分组后的排序和限制
            result = applyGroupingPostProcessing(result, queryContext);

            log.info("带分组的指标查询完成, 最终结果数量: {}", result.size());
            return new PageResponse<>(result, result.size());

        } catch (IndicatorQueryException e) {
            log.error("带分组的指标查询失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("带分组的指标查询发生未知错误, connectionId: {}, dataModelId: {}",
                connectionId, request.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("带分组的指标查询", e);
        }
    }

    /**
     * 使用查询上下文处理数据查询
     *
     * @param queryContext 查询上下文
     * @return {@link PageResponse }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/07/23 18:52:08
     */
    private PageResponse<Map<String, Object>> handleDataQueryWithContext(QueryContext queryContext) {
        return dataQueryProcessor.processDataQuery(queryContext);
    }

    /**
     * 执行数据分组
     *
     * @param items        项目
     * @param queryContext 查询上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/07/23 18:52:12
     */
    private List<Map<String, Object>> performDataGrouping(List<Map<String, Object>> items,
        QueryContext queryContext) {
        try {
            log.debug("开始执行数据分组, 数据量: {}", items.size());

            List<String> statisticDims = queryContext.getStatisticDimensions();
            IndicatorSortField sortField = queryContext.getRequest().getSortField();
            String sortFieldName = sortField != null ? sortField.getField() : null;

            // 执行分组操作，传入排序字段名称以便保留排序字段信息
            List<Map<String, Object>> result = dataGrouping.groupIndicatorData(items, statisticDims, sortFieldName);

            log.debug("数据分组完成, 结果数量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("数据分组失败, dataModelId: {}", queryContext.getDataModelId(), e);
            throw IndicatorQueryException.groupingError("数据分组处理", e);
        }
    }

    /**
     * 应用分组后的处理逻辑（排序和限制）
     *
     * @param result       结果
     * @param queryContext 查询上下文
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/07/23 18:52:15
     */
    private List<Map<String, Object>> applyGroupingPostProcessing(List<Map<String, Object>> result,
        QueryContext queryContext) {
        try {
            // 检查是否真正发生了分组
            boolean actuallyGrouped = isActuallyGrouped(result);

            if (actuallyGrouped) {
                log.debug("数据已分组，应用分组后处理逻辑");

                IndicatorSortField sortField = queryContext.getRequest().getSortField();
                if (sortField != null) {
                    // 检查排序字段是否在分组结果中存在
                    boolean sortFieldExists = !result.isEmpty() && result.get(0).containsKey(sortField.getField());

                    if (sortFieldExists) {
                        result = IndicatorDataSorter.sort(result, sortField);
                        log.debug("分组结果排序完成，排序字段: {}", sortField.getField());
                    } else {
                        log.warn("排序字段 '{}' 在分组结果中不存在，跳过排序", sortField.getField());
                        // 对于固定排序，尝试基于GROUP_FIELD中的数据进行排序
                        if (sortField.getOrder() == IndicatorSortOrder.FIXED) {
                            result = applyFixedSortToGroupedData(result, sortField);
                        }
                    }
                }
            }

            // 根据 size 限制结果数量
            Integer size = queryContext.getRequest().getSize();
            if (size != null && size > 0 && result.size() > size) {
                result = limitResultsWithSameSortValue(result, size, queryContext.getRequest().getSortField());
                log.debug("结果数量限制完成, 限制后数量: {}", result.size());
            }

            return result;

        } catch (Exception e) {
            log.error("分组后处理失败, dataModelId: {}", queryContext.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("分组后处理", e);
        }
    }

    /**
     * 检查是否真正发生了分组
     *
     * @param result 结果
     * @return boolean
     * <AUTHOR>
     * @since 2025/07/23 18:52:24
     */
    private boolean isActuallyGrouped(List<Map<String, Object>> result) {
        return !result.isEmpty() && result.get(0).containsKey(GROUP_FIELD);
    }

    /**
     * 对分组数据应用固定排序
     * <p>
     * 当排序字段不在分组结果的顶层时，尝试从GROUP_FIELD中的数据获取排序值进行排序
     *
     * @param result    分组结果
     * @param sortField 固定排序字段
     * @return 排序后的结果
     */
    private List<Map<String, Object>> applyFixedSortToGroupedData(List<Map<String, Object>> result,
        IndicatorSortField sortField) {
        try {
            if (sortField.getFixedOrder() == null || sortField.getFixedOrder().isEmpty()) {
                log.debug("固定排序顺序为空，跳过排序");
                return result;
            }

            String sortFieldName = sortField.getField();
            List<String> fixedOrder = sortField.getFixedOrder();

            // 创建固定顺序的索引映射
            Map<String, Integer> orderMap = new HashMap<>();
            for (int i = 0; i < fixedOrder.size(); i++) {
                orderMap.put(fixedOrder.get(i), i);
            }

            // 对分组结果进行排序
            result.sort((item1, item2) -> {
                Object value1 = extractSortValueFromGroupedItem(item1, sortFieldName);
                Object value2 = extractSortValueFromGroupedItem(item2, sortFieldName);

                int index1 = orderMap.getOrDefault(String.valueOf(value1), Integer.MAX_VALUE);
                int index2 = orderMap.getOrDefault(String.valueOf(value2), Integer.MAX_VALUE);

                return Integer.compare(index1, index2);
            });

            log.debug("固定排序应用完成，排序字段: {}", sortFieldName);
            return result;

        } catch (Exception e) {
            log.error("应用固定排序失败，排序字段: {}", sortField.getField(), e);
            return result;
        }
    }

    /**
     * 从分组项中提取排序值
     * <p>
     * 优先从顶层获取，如果不存在则从GROUP_FIELD中的第一个数据项获取
     *
     * @param groupedItem   分组项
     * @param sortFieldName 排序字段名
     * @return 排序值
     */
    private Object extractSortValueFromGroupedItem(Map<String, Object> groupedItem, String sortFieldName) {
        // 首先尝试从顶层获取
        Object value = groupedItem.get(sortFieldName);
        if (value != null) {
            return value;
        }

        // 如果顶层没有，尝试从GROUP_FIELD中获取
        Object groupField = groupedItem.get(GROUP_FIELD);
        if (groupField instanceof List<?> groupData && !groupData.isEmpty()) {
            Object firstItem = groupData.get(0);
            if (firstItem instanceof Map<?, ?> firstMap) {
                return firstMap.get(sortFieldName);
            }
        }

        return null;
    }

    /**
     * 限制结果数量，但保持相同排序值的记录完整性
     * <p>
     * 当指定返回 N 条记录时，如果第 N 条和第 N+1 条记录的排序字段值相同， 则将所有具有相同排序值的记录都包含在结果中，确保数据的完整性和一致性。
     *
     * @param result    原始结果列表
     * @param size      期望的结果数量
     * @param sortField 排序字段，如果为null则使用严格截断
     * @return 限制后的结果列表
     */
    private List<Map<String, Object>> limitResultsWithSameSortValue(final List<Map<String, Object>> result,
        final Integer size, final IndicatorSortField sortField) {

        // 如果没有排序字段，使用简单截断
        if (sortField == null) {
            return result.subList(0, size);
        }

        final String sortFieldName = sortField.getField();

        // 获取第 size 条记录的排序字段值
        final Object thresholdValue = result.get(size - 1).get(sortFieldName);

        // 特殊情况处理：当排序字段在分组后的数据中不存在时
        // 这通常发生在数据经过分组处理，但排序字段不是统计维度字段的情况下
        if (thresholdValue == null) {
            log.debug("排序字段 '{}' 在分组数据中不存在，使用简单截断逻辑", sortFieldName);
            return result.subList(0, size);
        }

        // 找到所有与第 size 条记录具有相同排序值的记录的结束位置
        int endIndex = size;
        while (endIndex < result.size()) {
            final Object currentValue = result.get(endIndex).get(sortFieldName);
            if (!CalculationUtils.isSameValue(thresholdValue, currentValue)) {
                break;
            }
            endIndex++;
        }

        return result.subList(0, endIndex);
    }

    /**
     * 核心处理方法，负责编排整个指标查询和计算流程
     *
     * @param dataConnection 数据连接信息
     * @param request        指标数据查询参数
     * @return 处理后的分页结果
     */
    private PageResponse<Map<String, Object>> handleDataQuery(DataConnection dataConnection,
        IndicatorDataSearchParams request) {
        try {
            log.debug("开始处理数据查询, dataModelId: {}", request.getDataModelId());

            // 构建查询上下文
            FieldGroups fieldGroups = getFieldGroups(request.getDataModelId());
            QueryContext queryContext = dataConnectionValidator.buildQueryContext(
                dataConnection, request, fieldGroups);

            // 委托给 DataQueryProcessor 处理
            PageResponse<Map<String, Object>> result = dataQueryProcessor.processDataQuery(queryContext);

            if (CollectionUtils.isNotEmpty(result.getItems())) {
                log.debug("数据查询处理完成, 结果数量: {}", result.getItems().size());
            }
            return result;
        } catch (IndicatorQueryException e) {
            log.error("数据查询处理失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("数据查询处理发生未知错误, dataModelId: {}", request.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("数据查询处理", e);
        }
    }

    /**
     * 获取字段组
     *
     * @param dataModelId 数据型id
     * @return {@link FieldGroups }
     * <AUTHOR>
     * @since 2025/07/15 09:31:00
     */
    public FieldGroups getFieldGroups(Integer dataModelId) {
        List<DataModelIndicatorField> allFields = getIndicatorFields(dataModelId);

        List<DataModelIndicatorField> metaFields = allFields.stream()
            .filter(e -> e.getIndicatorType() == IndicatorFieldType.META).toList();

        List<DataModelIndicatorField> applicationFields = allFields.stream()
            .filter(e -> e.getIndicatorType() == IndicatorFieldType.APPLICATION).toList();

        String startTimeField = findTimeField(metaFields, "start_time");
        String endTimeField = findTimeField(metaFields, "end_time");

        return new FieldGroups(metaFields, applicationFields, startTimeField, endTimeField);
    }

    private List<DataModelIndicatorField> getIndicatorFields(Integer dataModelId) {
        List<DataModelField> dataModelFields = dataModelFieldMapper.selectByDataModelId(dataModelId);
        List<DataModelIndicatorFieldEntity> fieldEntities = indicatorFieldMapper.listByDataModelId(dataModelId);

        Map<Integer, DataModelField> fieldMap = dataModelFields.stream()
            .collect(Collectors.toMap(DataModelField::getId, Function.identity()));

        return fieldEntities.stream().map(field -> {
            DataModelField dataModelField = fieldMap.get(field.getDataModelFieldId());
            if (dataModelField == null) {
                return null;
            }
            return new DataModelIndicatorField(dataModelField, field.getIndicatorType(), field.isEnable(),
                field.getOriginalZhName(), field.getConfig(), field.getExecuteOrder());
        }).filter(Objects::nonNull).toList();
    }

    private String findTimeField(List<DataModelIndicatorField> fields, String fieldNamePart) {
        return fields.stream().filter(e -> e.getEnName().contains(fieldNamePart)).findFirst()
            .map(DataModelIndicatorField::getEnName).orElse(null);
    }

    /**
     * 字段组
     *
     * @param metaFields        元字段列表
     * @param applicationFields 应用字段列表
     * @param startTimeField    开始时间字段
     * @param endTimeField      结束时间字段
     * <AUTHOR>
     * @since 2025/06/03 20:32:54
     */
    public record FieldGroups(List<DataModelIndicatorField> metaFields, List<DataModelIndicatorField> applicationFields,
                              String startTimeField, String endTimeField) {

        /**
         * 获取应用程序字段名称
         *
         * @return {@link List }<{@link String }>
         * <AUTHOR>
         * @since 2025/07/15 17:03:45
         */
        public List<String> getApplicationFieldNames() {
            return applicationFields.stream().map(DataModelIndicatorField::getEnName).toList();
        }
    }

}
